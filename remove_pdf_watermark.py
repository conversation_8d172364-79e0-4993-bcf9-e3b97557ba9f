import pikepdf
import argparse
import os

def main():
    # 设置命令行参数解析
    parser = argparse.ArgumentParser(description='Remove watermark text from PDF files')
    parser.add_argument('input_pdf', help='Input PDF file path')
    parser.add_argument('watermark_text', help='Watermark text to remove')
    parser.add_argument('-o', '--output', help='Output PDF file path (default: adds "_cleaned" suffix to input filename)')

    args = parser.parse_args()

    input_pdf = args.input_pdf
    watermark_text = args.watermark_text

    # 如果没有指定输出文件名，则自动生成
    if args.output:
        output_pdf = args.output
    else:
        # 从输入文件名生成输出文件名
        base_name = os.path.splitext(input_pdf)[0]
        extension = os.path.splitext(input_pdf)[1]
        output_pdf = f"{base_name}_cleaned{extension}"

    # 检查输入文件是否存在
    if not os.path.exists(input_pdf):
        print(f"错误：输入文件 '{input_pdf}' 不存在")
        return

    try:
        # 打开 PDF
        pdf = pikepdf.open(input_pdf)

        # 遍历所有页面对象
        for page_num, page in enumerate(pdf.pages):
            print(f"处理第 {page_num + 1} 页...")

            # 解析页面内容流
            content = page.Contents
            if content is not None:
                # 检查 Contents 是否为数组
                if isinstance(content, pikepdf.Array):
                    print(f"  页面 {page_num + 1} 包含 {len(content)} 个内容流")
                    # 如果是数组，需要合并所有流并重新处理
                    combined_stream = b""
                    for i, stream_ref in enumerate(content):
                        try:
                            # 解引用获取实际的流对象
                            stream_obj = pdf.resolve(stream_ref)
                            if hasattr(stream_obj, 'read_bytes'):
                                stream_data = stream_obj.read_bytes()
                                combined_stream += stream_data + b"\n"
                        except Exception as e:
                            print(f"    警告：无法读取流 {i}: {e}")
                            continue

                    # 处理合并后的流
                    if combined_stream:
                        try:
                            stream_text = combined_stream.decode("latin-1")
                            # 替换掉水印文字
                            if watermark_text in stream_text:
                                print(f"  在页面 {page_num + 1} 找到水印文字，正在移除...")
                                stream_text = stream_text.replace(watermark_text, "")
                                # 创建新的流对象替换原来的内容
                                new_stream = pdf.make_stream(stream_text.encode("latin-1"))
                                page.Contents = new_stream
                            else:
                                print(f"  页面 {page_num + 1} 未找到指定的水印文字")
                        except UnicodeDecodeError as e:
                            print(f"    警告：页面 {page_num + 1} 内容解码失败: {e}")
                            continue

                else:
                    # 如果是单个流对象
                    try:
                        if hasattr(content, 'read_bytes'):
                            stream_data = content.read_bytes()
                            stream_text = stream_data.decode("latin-1")
                            # 替换掉水印文字
                            if watermark_text in stream_text:
                                print(f"  在页面 {page_num + 1} 找到水印文字，正在移除...")
                                stream_text = stream_text.replace(watermark_text, "")
                                # 写回页面
                                content.write(stream_text.encode("latin-1"))
                            else:
                                print(f"  页面 {page_num + 1} 未找到指定的水印文字")
                    except UnicodeDecodeError as e:
                        print(f"    警告：页面 {page_num + 1} 内容解码失败: {e}")
                        continue
                    except Exception as e:
                        print(f"    警告：处理页面 {page_num + 1} 时出错: {e}")
                        continue

        # 保存新文件
        pdf.save(output_pdf)
        pdf.close()

        print(f"已生成无水印版本：{output_pdf}")

    except Exception as e:
        print(f"处理PDF时发生错误：{e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()